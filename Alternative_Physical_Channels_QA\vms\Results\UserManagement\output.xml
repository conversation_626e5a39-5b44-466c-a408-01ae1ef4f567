<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-18T11:43:38.664124" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-224_Validate_Search_-_ATM_Number_Column_-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.444440" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.444440" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.444440" elapsed="0.001547"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.446139" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.446139" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.446139" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.447044" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.447044" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.447044" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.448046" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:43.448046" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.448046" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.449093" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.449093" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.448046" elapsed="0.001047"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.450090" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.450090" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.449093" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.453112" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:43.452077" elapsed="0.001035"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.451089" elapsed="0.002023"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.455081" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:43.455081" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.454082" elapsed="0.000999"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-18T11:43:43.459011" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:43.459011" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:43:43.456080" elapsed="0.002931"/>
</branch>
<status status="PASS" start="2025-06-18T11:43:43.456080" elapsed="0.004192"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.461304" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:43.461304" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.460272" elapsed="0.001032"/>
</kw>
<status status="PASS" start="2025-06-18T11:43:43.440876" elapsed="0.021421"/>
</kw>
<test id="s1-t1" name="Validate Search - ATM Number Coloumn - on ATM Details" line="40">
<kw name="Validate Search - ATM Number Coloumn - on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.464312" level="INFO">Set test documentation to:
Validate Search - ATM Number Coloumn - on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-18T11:43:43.464312" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.465850" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:43:43.464312" elapsed="0.001538"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:43:43.466386" elapsed="0.001130"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:43:43.467516" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.470550" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-18T11:43:43.470550" elapsed="0.001002">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-18T11:43:43.471552" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T11:43:43.469544" elapsed="0.002008"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:43.472549" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:43:43.471552" elapsed="0.000997"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.473554" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:43.472549" elapsed="0.001005"/>
</kw>
<status status="PASS" start="2025-06-18T11:43:43.472549" elapsed="0.001005"/>
</branch>
<status status="PASS" start="2025-06-18T11:43:43.471552" elapsed="0.002002"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-18T11:43:43.492500" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-18T11:43:43.492500" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-18T11:43:43.474554" elapsed="0.017946"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.500535" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:43:43.500535" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.501566" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:43.501566" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:43:43.500535" elapsed="0.001031"/>
</branch>
<status status="PASS" start="2025-06-18T11:43:43.500535" elapsed="0.001031"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-18T11:43:43.501566" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-18T11:43:43.501566" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:43:43.502599" elapsed="0.000992"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-18T11:43:43.906106" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-18T11:43:44.876168" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-18T11:43:44.876251" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-18T11:43:43.503591" elapsed="1.372660"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.878224" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:44.878224" elapsed="0.001000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:44.876251" elapsed="0.003994"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-18T11:43:43.494497" elapsed="1.385748"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:44.882232" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-18T11:43:44.881215" elapsed="0.001017"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T11:43:44.882232" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T11:43:44.882232" elapsed="0.001003"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T11:43:44.883235" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T11:43:44.883235" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:43:44.884226" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:43:44.884226" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.887293" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:44.886775" elapsed="0.000518"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-18T11:43:44.888321" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001E89A339430&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-18T11:43:44.887293" elapsed="0.001028"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-18T11:43:44.888321" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:44.889338" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:43:44.888321" elapsed="0.001017"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.890373" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001E89A345D30&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:43:44.889338" elapsed="0.001035"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.891341" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:44.890373" elapsed="0.000968"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.892345" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:44.891341" elapsed="0.001004"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.892345" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:43:44.892345" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-18T11:43:44.894354" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-18T11:43:44.893360" elapsed="0.000994"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-18T11:43:44.894354" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-18T11:43:44.895966" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T11:43:44.896918" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-18T11:43:44.894354" elapsed="5.148656"/>
</kw>
<status status="PASS" start="2025-06-18T11:43:44.889338" elapsed="5.153672"/>
</branch>
<status status="PASS" start="2025-06-18T11:43:44.888321" elapsed="5.154689"/>
</if>
<status status="PASS" start="2025-06-18T11:43:44.885747" elapsed="5.157263"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.044015" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.044015" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.045015" elapsed="0.001002"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.046543" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:43:50.044015" elapsed="0.003559"/>
</branch>
<status status="NOT RUN" start="2025-06-18T11:43:50.044015" elapsed="0.003559"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.047574" elapsed="0.001026"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.048600" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.050000" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.050000" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.051036" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.052513" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.052513" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.053534" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.053534" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.053534" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.054538" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.054538" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T11:43:50.054538" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:43:50.043010" elapsed="0.012375"/>
</branch>
<status status="PASS" start="2025-06-18T11:43:44.884226" elapsed="5.171159"/>
</if>
<status status="PASS" start="2025-06-18T11:43:43.493498" elapsed="6.561887"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-18T11:43:50.057969" elapsed="0.054348"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-18T11:43:50.118914" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-18T11:43:50.117886" elapsed="2.756646"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:50.113319" elapsed="2.762092"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:02.877357" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:43:52.876048" elapsed="10.001309"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:02.879395" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T11:44:02.919519" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:02.879395" elapsed="0.041132"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T11:44:02.921521" elapsed="0.001004"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:12.924000" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:02.923520" elapsed="10.001060"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:12.924580" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T11:44:12.956043" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:12.924580" elapsed="0.031704"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T11:44:12.956284" elapsed="0.000840"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:17.958439" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:12.958152" elapsed="5.000287"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:17.959014" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T11:44:17.993839" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:17.959014" elapsed="0.034825"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T11:44:17.994884" elapsed="0.000963"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-18T11:43:50.055974" elapsed="27.939873"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:18.068552" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T11:44:17.998402" elapsed="0.070150"/>
</kw>
<msg time="2025-06-18T11:44:18.069547" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T11:44:17.996366" elapsed="0.074188"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:18.074544" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:18.073544" elapsed="0.248567"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T11:44:18.323109" elapsed="0.055899"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:18.380006" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:18.379008" elapsed="0.287816"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:18.666824" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:18.666824" elapsed="3.018055"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:23.686344" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:21.684879" elapsed="2.001659"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:23.898432" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-882.png"&gt;&lt;img src="selenium-screenshot-882.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T11:44:23.898432" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-18T11:44:23.686538" elapsed="0.214899">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-18T11:44:23.902440" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T11:44:23.686538" elapsed="0.215902"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:18.070554" elapsed="5.831886"/>
</iter>
<status status="PASS" start="2025-06-18T11:44:18.070554" elapsed="5.831886"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:23.902440" elapsed="0.001002"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:24.096579" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-883.png"&gt;&lt;img src="selenium-screenshot-883.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-18T11:44:23.903442" elapsed="0.193137"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-18T11:43:43.469544" elapsed="40.627035"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:43:43.468554" elapsed="40.628025"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-18T11:43:43.464312" elapsed="40.632267"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T11:44:24.097606" elapsed="0.040258"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:24.137864" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:24.137864" elapsed="2.573503"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:26.712375" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:24.096579" elapsed="2.616791"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:31.718491" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:26.717172" elapsed="5.001844"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:31.826998" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:31.720042" elapsed="0.106956"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:31.922338" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:31.826998" elapsed="0.095340"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.017445" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:31.923340" elapsed="0.094105"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.115137" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.017445" elapsed="0.097692"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.206173" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.116208" elapsed="0.089965"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.285845" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.207198" elapsed="0.078850"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.378104" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.286866" elapsed="0.092270"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.474922" level="INFO">Current page contains text 'ATM Name'.</msg>
<arg>ATM Name</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.379136" elapsed="0.095786"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.570387" level="INFO">Current page contains text 'ATM Address'.</msg>
<arg>ATM Address</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.475928" elapsed="0.094459"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.657761" level="INFO">Current page contains text 'City'.</msg>
<arg>City</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.571380" elapsed="0.086381"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.740495" level="INFO">Current page contains text 'Province'.</msg>
<arg>Province</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.657761" elapsed="0.083738"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.836992" level="INFO">Current page contains text 'Zone SLA'.</msg>
<arg>Zone SLA</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.741499" elapsed="0.095493"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.926525" level="INFO">Current page contains text 'Action'.</msg>
<arg>Action</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.838057" elapsed="0.089477"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:32.927534" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:26.714571" elapsed="6.213975"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:32.932284" level="INFO">Clicking element 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:32.932284" elapsed="0.260789"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked Search Element</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:33.194083" elapsed="0.000998"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:38.196455" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:33.195081" elapsed="5.001374"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:38.198484" level="INFO">Typing text 'S08003' into text field 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<arg>${SEARCH_KEY}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:38.197471" elapsed="0.259122"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has input Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:38.457602" elapsed="0.001011"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>${SEARCH_KEY}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-06-18T11:44:38.458613" elapsed="0.075656"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>-------------------------- The user has waited and found the Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:38.534269" elapsed="0.001508"/>
</kw>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-18T11:44:32.929655" elapsed="5.606122"/>
</kw>
<kw name="Then The user verifies only one row is data present" owner="ATMDetails">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.537957" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:38.537290" elapsed="3.000667"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.569094" level="INFO">${datarows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.197")&gt;]</msg>
<var>${datarows}</var>
<arg>//*[@id="root"]/div/table//tbody//tr</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.539083" elapsed="0.030011"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.571147" level="INFO">Length is 1.</msg>
<msg time="2025-06-18T11:44:41.571147" level="INFO">${row_count} = 1</msg>
<var>${row_count}</var>
<arg>${datarows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-18T11:44:41.570136" elapsed="0.001011"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.572136" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${row_count}</arg>
<arg>1</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-06-18T11:44:41.571147" elapsed="0.000989"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has verified the default number of rows</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.573180" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:38.536784" elapsed="3.036396"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T11:44:41.577934" elapsed="0.064750"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Column Element Has Been Found: ${COLUMN}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.643745" elapsed="0.000898"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.673797" level="INFO">${all_headers} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.198")&gt;, &lt;selenium.webdri...</msg>
<var>${all_headers}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.646250" elapsed="0.027547"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.694618" level="INFO">${header_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.198")&gt;</msg>
<var>${header_element}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.673797" elapsed="0.020821"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.717996" level="INFO">${header_element_text} = ATM Number</msg>
<var>${header_element_text}</var>
<arg>${header_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.694618" elapsed="0.023378"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.720014" level="INFO">${cleaned_header_element_text} = ATMNumber</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:44:41.718982" elapsed="0.001032"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T11:44:41.720981" level="INFO">${cleaned_h_element_text} = ATMNumber</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.720014" elapsed="0.000967"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.721984" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.724095" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:44:41.723106" elapsed="0.000989"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.725076" level="INFO">Length is 12.</msg>
<msg time="2025-06-18T11:44:41.725076" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>${all_headers}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-18T11:44:41.724095" elapsed="0.000981"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.727646" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T11:44:41.727646" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.198")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.727646" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.744132" level="INFO">${header_text} = ATM Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.727646" elapsed="0.016486"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.745115" level="INFO">${cleaned_header_text} = ATMNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:44:41.745115" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T11:44:41.746627" level="INFO">${cleaned_h_text} = ATMNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.745115" elapsed="0.001512"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.746627" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.747638" level="INFO">${index} = 0</msg>
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:44:41.747638" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.747638" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.748634" level="INFO">Exiting for loop altogether.</msg>
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="PASS" start="2025-06-18T11:44:41.748634" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:41.746627" elapsed="0.002007"/>
</branch>
<status status="PASS" start="2025-06-18T11:44:41.746627" elapsed="0.002007"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-18T11:44:41.726633" elapsed="0.022001"/>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="PASS" start="2025-06-18T11:44:41.726020" elapsed="0.022614"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Column with text '${COLUMN}' not found.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:44:41.748634" elapsed="0.000000"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.768789" level="INFO">${elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.210")&gt;]</msg>
<var>${elements}</var>
<arg>xpath=*//tbody//tr//td[${index} + 1]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.748634" elapsed="0.020155"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.768789" level="INFO">Length is 1.</msg>
<msg time="2025-06-18T11:44:41.768789" level="INFO">${num_elements} = 1</msg>
<var>${num_elements}</var>
<arg>${elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-18T11:44:41.768789" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.769788" level="INFO">${found} = False</msg>
<var>${found}</var>
<arg>False</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:44:41.768789" elapsed="0.000999"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T11:44:41.770791" level="INFO">${specific_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="4c4459d68ecb3b59da1aa00a8be1c593", element="f.6D293608B6367AE367E349826B8153CF.d.7D21B456BDA2597B77605987E861E4BA.e.210")&gt;</msg>
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${e}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.769788" elapsed="0.001003"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.791037" level="INFO">${element_text} = S08003</msg>
<var>${element_text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.770791" elapsed="0.020246"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Search Result Retrieved: ${element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.791993" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.792989" level="INFO">${element_text} = S08003</msg>
<var>${element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:44:41.791993" elapsed="0.000996"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T11:44:41.792989" level="INFO">${element_t} = S08003</msg>
<var>${element_t}</var>
<arg>${element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.792989" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.793990" level="INFO">${search_key} = S08003</msg>
<var>${search_key}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${SEARCH_KEY}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:44:41.793990" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T11:44:41.793990" level="INFO">${search_k} = S08003</msg>
<var>${search_k}</var>
<arg>${search_key}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.793990" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$element_t == $search_k">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.794990" level="INFO">${found} = True</msg>
<var>${found}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:44:41.794990" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------- Search Key was found in the correct column.</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.794990" elapsed="0.000997"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<msg time="2025-06-18T11:44:41.795987" level="INFO">Exiting for loop altogether.</msg>
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="PASS" start="2025-06-18T11:44:41.795987" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:41.794990" elapsed="0.000997"/>
</branch>
<status status="PASS" start="2025-06-18T11:44:41.794990" elapsed="0.000997"/>
</if>
<var name="${e}">0</var>
<status status="PASS" start="2025-06-18T11:44:41.769788" elapsed="0.026199"/>
</iter>
<var>${e}</var>
<value>${num_elements}</value>
<status status="PASS" start="2025-06-18T11:44:41.769788" elapsed="0.026199"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${found}' == 'False'</arg>
<arg>Fail</arg>
<arg>Search Key was NOT found in the correct column.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:44:41.795987" elapsed="0.001189"/>
</kw>
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-18T11:44:41.575950" elapsed="0.221226"/>
</kw>
<arg>Validate Search - ATM Number Coloumn - on ATM Details</arg>
<arg>VMS_UAT</arg>
<arg>S08003</arg>
<arg>ATM Number</arg>
<status status="PASS" start="2025-06-18T11:43:43.463327" elapsed="58.333849"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T11:44:41.797176" elapsed="0.033473"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:44:41.830649" elapsed="0.001004"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T11:44:41.831653" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T11:44:41.831653" elapsed="3.037158"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:44:47.870944" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:44:44.869803" elapsed="3.001141"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-18T11:44:47.870944" elapsed="11.210684"/>
</kw>
<status status="PASS" start="2025-06-18T11:44:41.797176" elapsed="17.284452"/>
</kw>
<doc>Validate Search - ATM Number Coloumn - on ATM Details</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-18T11:43:43.462297" elapsed="75.620329"/>
</test>
<doc>Validate Search - ATM Number Coloumn - on ATM Details</doc>
<status status="PASS" start="2025-06-18T11:43:38.720949" elapsed="80.365266"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">ATM DETAILS</stat>
<stat pass="1" fail="0" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-18T11:43:38.713313" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T11:43:42.261191" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 177: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.262189" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 207: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.262189" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 238: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.263186" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 277: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.264188" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 321: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.264188" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 333: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.265848" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 376: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.268812" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 629: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.268812" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 651: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.269811" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 682: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.270857" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 718: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.271814" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 728: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.272809" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 739: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.273807" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 757: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.274837" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 764: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.276337" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 784: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.279389" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 809: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.280392" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 840: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.284810" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 946: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.288413" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1172: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.291409" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1255: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.347454" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 116: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.347454" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 136: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.410805" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:42.411812" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 370: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:43:43.394983" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T11:43:43.428178" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-18T11:43:44.878224" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-18T11:43:44.895966" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T11:44:02.879395" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T11:44:12.924580" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T11:44:17.959014" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
