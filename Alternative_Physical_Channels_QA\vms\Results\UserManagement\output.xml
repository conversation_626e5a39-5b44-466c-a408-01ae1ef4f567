<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-18T11:22:46.377962" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-220_-_Validate_Accessing_the_ATM_Details_Screen-_on_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.214780" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.214780" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.213774" elapsed="0.001006"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.215785" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.215785" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.214780" elapsed="0.001005"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.216325" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.216325" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.216325" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.217053" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:49.217053" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.217053" elapsed="0.000897"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.218746" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.218551" elapsed="0.000195"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.218232" elapsed="0.000514"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.218746" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.218746" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.218746" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.219774" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:49.219774" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.218746" elapsed="0.001028"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.219774" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:49.219774" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.219774" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-18T11:22:49.220774" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:49.220774" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:49.220774" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T11:22:49.219774" elapsed="0.001000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.220774" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:49.220774" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:49.220774" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:22:49.212482" elapsed="0.009292"/>
</kw>
<test id="s1-t1" name="Validate Accessing the ATM Details Screen- on ATM Details" line="34">
<kw name="Validate Accessing ATM Details Page">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.222773" level="INFO">Set test documentation to:
Validate Accessing ATM Details Page</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-18T11:22:49.222773" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.222773" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:22:49.222773" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:22:49.223771" elapsed="0.000158"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:22:49.223929" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.225444" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-18T11:22:49.223929" elapsed="0.001515">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-18T11:22:49.225444" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T11:22:49.223929" elapsed="0.001515"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:49.225444" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:49.225444" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.225444" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:49.225444" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:22:49.225444" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T11:22:49.225444" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-18T11:22:49.253278" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-18T11:22:49.253278" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-18T11:22:49.225444" elapsed="0.027834"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.255274" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:22:49.254277" elapsed="0.000997"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.255274" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:49.255274" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T11:22:49.255274" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T11:22:49.255274" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-18T11:22:49.255274" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-18T11:22:49.255274" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:22:49.256273" elapsed="0.000194"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-18T11:22:49.448517" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-18T11:22:50.110508" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-18T11:22:50.110508" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-18T11:22:49.256467" elapsed="0.854041"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-18T11:22:49.254277" elapsed="0.856231"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000024B3D0778C0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000024B3D085880&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.110508" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-18T11:22:50.124822" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="0.014314"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-18T11:22:50.124822" elapsed="0.001027"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-18T11:22:50.125849" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T11:22:50.127026" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-18T11:22:50.125849" elapsed="3.933210"/>
</kw>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="3.948551"/>
</branch>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="3.948551"/>
</if>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="3.948551"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.062490" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.062490" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.062490" elapsed="0.001033"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.063523" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.063523" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.063523" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:54.063523" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:54.059059" elapsed="0.004464"/>
</branch>
<status status="PASS" start="2025-06-18T11:22:50.110508" elapsed="3.953015"/>
</if>
<status status="PASS" start="2025-06-18T11:22:49.254277" elapsed="4.809246"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<msg time="2025-06-18T11:22:55.235542" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-874.png"&gt;&lt;img src="selenium-screenshot-874.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T11:22:55.235542" level="FAIL">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</msg>
<doc>Maximizes current browser window.</doc>
<status status="FAIL" start="2025-06-18T11:22:54.063523" elapsed="1.225271">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="FAIL" start="2025-06-18T11:22:54.063523" elapsed="1.225271">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</iter>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="FAIL" start="2025-06-18T11:22:49.223929" elapsed="6.064865">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="FAIL" start="2025-06-18T11:22:49.223929" elapsed="6.064865">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="FAIL" start="2025-06-18T11:22:49.222773" elapsed="6.066021">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-18T11:22:55.288794" elapsed="0.000000"/>
</kw>
<arg>Validate Accessing ATM Details Page</arg>
<arg>VMS_UAT</arg>
<status status="FAIL" start="2025-06-18T11:22:49.222773" elapsed="6.066021">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T11:23:00.632666" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-875.png"&gt;&lt;img src="selenium-screenshot-875.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T11:23:00.632666" level="FAIL">Element 'name=ctl00$btnLogout' not visible after 5 seconds.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-18T11:22:55.288794" elapsed="5.343872">Element 'name=ctl00$btnLogout' not visible after 5 seconds.</status>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T11:23:00.632666" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T11:23:00.632666" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg time="2025-06-18T11:23:00.799884" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-876.png"&gt;&lt;img src="selenium-screenshot-876.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T11:23:00.800884" level="FAIL">Button with locator 'name=ctl00$btnLogout' not found.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="FAIL" start="2025-06-18T11:23:00.632666" elapsed="0.198586">Button with locator 'name=ctl00$btnLogout' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T11:23:03.831455" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T11:23:00.831252" elapsed="3.000203"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-18T11:23:03.831455" elapsed="6.115980"/>
</kw>
<status status="FAIL" start="2025-06-18T11:22:55.288794" elapsed="14.658641">Several failures occurred:

1) Element 'name=ctl00$btnLogout' not visible after 5 seconds.

2) Button with locator 'name=ctl00$btnLogout' not found.</status>
</kw>
<doc>Validate Accessing ATM Details Page</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-18T11:22:49.221774" elapsed="20.727008">WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
  (Session info: MicrosoftEdge=137.0.3296.83)
Stacktrace:
	GetHandleVerifier [0x00007FF7584302E5+25029]
	(No symbol) [0x00007FF7583852F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7586AD73A+1947706]
	(No symbol) [0x00007FF75814C15D]
	(No symbol) [0x00007FF75814AAEC]
	(No symbol) [0x00007FF75814B21F]
	(No symbol) [0x00007FF75816F6C2]
	(No symbol) [0x00007FF75816462D]
	(No symbol) [0x00007FF758164516]
	(No symbol) [0x00007FF75813E86E]
	(No symbol) [0x00007FF75813D291]
	(No symbol) [0x00007FF7581FC6F1]
	(No symbol) [0x00007FF7581C908A]
	(No symbol) [0x00007FF75819E15D]
	(No symbol) [0x00007FF7581E6698]
	(No symbol) [0x00007FF7581C8DF3]
	(No symbol) [0x00007FF75819D6A6]
	(No symbol) [0x00007FF75819CBB3]
	(No symbol) [0x00007FF75819D4D3]
	(No symbol) [0x00007FF75829638D]
	(No symbol) [0x00007FF7582A3F2F]
	(No symbol) [0x00007FF75829C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7585176EA+284650]
	(No symbol) [0x00007FF758392C81]
	(No symbol) [0x00007FF75838B724]
	(No symbol) [0x00007FF75838B873]
	(No symbol) [0x00007FF75837D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]


Also teardown failed:
Several failures occurred:

1) Element 'name=ctl00$btnLogout' not visible after 5 seconds.

2) Button with locator 'name=ctl00$btnLogout' not found.</status>
</test>
<doc>Validate Details of All ATMs</doc>
<status status="FAIL" start="2025-06-18T11:22:46.441193" elapsed="23.507589"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">ATM DETAILS</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="0" fail="1" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-18T11:22:46.426131" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T11:22:48.907182" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 177: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.907182" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 207: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.913774" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 238: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.914309" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 277: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.914309" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 321: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.914309" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 333: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.914309" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 376: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 629: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 651: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 682: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 718: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 728: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 739: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 757: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 764: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.922126" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 784: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.937782" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 809: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.937782" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 840: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.941457" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 946: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.947283" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1172: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.947283" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1255: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.969594" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 116: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.969594" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 136: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.975029" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:48.975029" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 370: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T11:22:49.187669" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T11:22:49.203204" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-18T11:22:50.110508" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-18T11:22:50.125849" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
</errors>
</robot>
