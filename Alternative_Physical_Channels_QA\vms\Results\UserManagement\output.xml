<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-18T12:57:14.549295" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-234_Validate Search - Zone SLA Coloumn - on ATM Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.030095" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.030095" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.028694" elapsed="0.002431"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.031125" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.031125" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.031125" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.032500" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.032500" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.032500" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.034026" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:20.032500" elapsed="0.001526"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.032500" elapsed="0.001526"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.034026" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.034026" elapsed="0.001102"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.034026" elapsed="0.001102"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.035654" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.035654" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.035128" elapsed="0.000526"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.037147" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:20.037147" elapsed="0.001034"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.035654" elapsed="0.002527"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.039182" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:20.039182" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.038181" elapsed="0.001001"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-18T12:57:20.041376" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:20.041376" elapsed="0.001095"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:57:20.039182" elapsed="0.003289"/>
</branch>
<status status="PASS" start="2025-06-18T12:57:20.039182" elapsed="0.003289"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.043675" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:20.042471" elapsed="0.001204"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.042471" elapsed="0.001204"/>
</kw>
<status status="PASS" start="2025-06-18T12:57:20.027695" elapsed="0.015980"/>
</kw>
<test id="s1-t1" name="Validate Search - Zone SLA Column- on ATM Details" line="38">
<kw name="Validate Search - Zone SLA Column- on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.047331" level="INFO">Set test documentation to:
Validate Search - Zone SLA Column- on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-18T12:57:20.047331" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.049642" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:57:20.048668" elapsed="0.000974"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:57:20.049642" elapsed="0.001028"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:57:20.050670" elapsed="0.001345"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.056434" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-18T12:57:20.056434" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-18T12:57:20.058200" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T12:57:20.055410" elapsed="0.002790"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:20.058200" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:57:20.058200" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.058200" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:20.058200" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T12:57:20.058200" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T12:57:20.058200" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-18T12:57:20.079807" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-18T12:57:20.080446" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-18T12:57:20.058200" elapsed="0.022246"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.082229" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:57:20.082229" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.084231" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:20.083713" elapsed="0.000518"/>
</kw>
<status status="PASS" start="2025-06-18T12:57:20.082229" elapsed="0.002002"/>
</branch>
<status status="PASS" start="2025-06-18T12:57:20.082229" elapsed="0.002002"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-18T12:57:20.084778" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-18T12:57:20.084778" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:57:20.085461" elapsed="0.000595"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-18T12:57:20.305797" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-18T12:57:21.154273" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-18T12:57:21.154273" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-18T12:57:20.086056" elapsed="1.068217"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-18T12:57:20.082229" elapsed="1.072044"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000022DBD307080&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000022DBD315C10&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-18T12:57:21.154273" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:21.154273" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T12:57:21.154273" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="4.692736"/>
</kw>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="4.692736"/>
</branch>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="4.692736"/>
</if>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="4.692736"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.848249" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.848842" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.849945" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.849945" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:57:25.849376" elapsed="0.000569"/>
</branch>
<status status="NOT RUN" start="2025-06-18T12:57:25.849376" elapsed="0.001154"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.850530" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.850530" elapsed="0.000690"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.851220" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.851818" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.851818" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.851818" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.852867" elapsed="0.000480"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.854347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.855351" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.855798" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.855798" elapsed="0.001093"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.856891" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T12:57:25.858333" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:57:25.847009" elapsed="0.011324"/>
</branch>
<status status="PASS" start="2025-06-18T12:57:21.154273" elapsed="4.704060"/>
</if>
<status status="PASS" start="2025-06-18T12:57:20.081138" elapsed="5.777962"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-18T12:57:25.860702" elapsed="0.070640"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:25.942372" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-18T12:57:25.942372" elapsed="5.763215"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:25.931342" elapsed="5.774245"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:57:41.707285" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:57:31.705587" elapsed="10.001698"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:41.707285" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T12:57:41.733247" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:41.707285" elapsed="0.025962"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T12:57:41.733247" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:57:51.734736" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:57:41.734256" elapsed="10.000480"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:51.734736" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T12:57:51.738495" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:51.734736" elapsed="0.003759"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T12:57:51.738495" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:57:56.738719" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:57:51.738495" elapsed="5.000224"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:56.738719" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T12:57:56.772249" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:56.738719" elapsed="0.034542"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T12:57:56.774103" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-18T12:57:25.859822" elapsed="30.915406"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:56.825305" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T12:57:56.777225" elapsed="0.048080"/>
</kw>
<msg time="2025-06-18T12:57:56.825305" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T12:57:56.775228" elapsed="0.050077"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:56.825305" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:56.825305" elapsed="0.233674"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T12:57:57.070731" elapsed="0.068304"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:57.139035" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:57.139035" elapsed="0.649900"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T12:57:57.788935" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:57:57.788935" elapsed="3.816943"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:58:03.607235" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:58:01.606875" elapsed="2.000360"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:03.868457" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-909.png"&gt;&lt;img src="selenium-screenshot-909.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T12:58:03.868457" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-18T12:58:03.607235" elapsed="0.267404">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-18T12:58:03.874639" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T12:58:03.607235" elapsed="0.267404"/>
</kw>
<status status="PASS" start="2025-06-18T12:57:56.825305" elapsed="7.049334"/>
</iter>
<status status="PASS" start="2025-06-18T12:57:56.825305" elapsed="7.049334"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:03.874639" elapsed="0.001011"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:04.038441" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-910.png"&gt;&lt;img src="selenium-screenshot-910.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-18T12:58:03.875650" elapsed="0.162791"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-18T12:57:20.053319" elapsed="43.985122"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T12:57:20.052015" elapsed="43.986426"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-18T12:57:20.048366" elapsed="43.990075"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T12:58:04.039520" elapsed="0.031228"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:04.070748" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:04.070748" elapsed="4.612844"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:08.683592" elapsed="0.000995"/>
</kw>
<status status="PASS" start="2025-06-18T12:58:04.038441" elapsed="4.646146"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:58:13.684870" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:58:08.684587" elapsed="5.000283"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:13.767810" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:13.684870" elapsed="0.082940"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:13.836520" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:13.767810" elapsed="0.068710"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:13.921579" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:13.836520" elapsed="0.085059"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:13.986045" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:13.921579" elapsed="0.064466"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.058780" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:13.986045" elapsed="0.072735"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.121171" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.058780" elapsed="0.062391"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.200942" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.121171" elapsed="0.079771"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.263712" level="INFO">Current page contains text 'ATM Name'.</msg>
<arg>ATM Name</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.200942" elapsed="0.062770"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.359889" level="INFO">Current page contains text 'ATM Address'.</msg>
<arg>ATM Address</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.263712" elapsed="0.096177"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.483077" level="INFO">Current page contains text 'City'.</msg>
<arg>City</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.359889" elapsed="0.123188"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.602750" level="INFO">Current page contains text 'Province'.</msg>
<arg>Province</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.483077" elapsed="0.119673"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.686651" level="INFO">Current page contains text 'Zone SLA'.</msg>
<arg>Zone SLA</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.602750" elapsed="0.083901"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.770324" level="INFO">Current page contains text 'Action'.</msg>
<arg>Action</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.702275" elapsed="0.083720"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:14.785995" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T12:58:08.684587" elapsed="6.103107"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:14.788650" level="INFO">Clicking element 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:14.788650" elapsed="0.197005"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked Search Element</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:14.987246" elapsed="0.000591"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:58:19.988591" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:58:14.987837" elapsed="5.000754"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:19.988591" level="INFO">Typing text '4' into text field 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<arg>${SEARCH_KEY}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:19.988591" elapsed="0.145000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has input Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.133591" elapsed="0.003960"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>${SEARCH_KEY}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-06-18T12:58:20.137551" elapsed="0.070031"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>-------------------------- The user has waited and found the Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.207582" elapsed="0.000000"/>
</kw>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-18T12:58:14.788650" elapsed="5.418932"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T12:58:20.207582" elapsed="0.040485"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Column Element Has Been Found: ${COLUMN}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.248067" elapsed="0.000000"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.259729" level="INFO">${all_headers} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.148")&gt;, &lt;selenium.webdri...</msg>
<var>${all_headers}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.248067" elapsed="0.011662"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.279169" level="INFO">${header_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.182")&gt;</msg>
<var>${header_element}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.259729" elapsed="0.019440"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.304903" level="INFO">${header_element_text} = Zone SLA</msg>
<var>${header_element_text}</var>
<arg>${header_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.279169" elapsed="0.025734"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.304903" level="INFO">${cleaned_header_element_text} = ZoneSLA</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T12:58:20.304903" level="INFO">${cleaned_h_element_text} = ZoneSLA</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.304903" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.304903" level="INFO">Length is 12.</msg>
<msg time="2025-06-18T12:58:20.304903" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>${all_headers}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T12:58:20.304903" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.148")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.327874" level="INFO">${header_text} = ATM Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.022971"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.327874" level="INFO">${cleaned_header_text} = ATMNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T12:58:20.327874" level="INFO">${cleaned_h_text} = ATMNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.000000"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-18T12:58:20.304903" elapsed="0.022971"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.007001"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T12:58:20.334875" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.154")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.334875" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.347278" level="INFO">${header_text} = Serial Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.334875" elapsed="0.012403"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.347278" level="INFO">${cleaned_header_text} = SerialNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:58:20.347278" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T12:58:20.347278" level="INFO">${cleaned_h_text} = SerialNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.347278" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.347278" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.347278" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.354336" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.354336" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.347278" elapsed="0.007058"/>
</branch>
<status status="PASS" start="2025-06-18T12:58:20.347278" elapsed="0.007058"/>
</if>
<var name="${i}">1</var>
<status status="PASS" start="2025-06-18T12:58:20.327874" elapsed="0.026462"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.354336" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T12:58:20.355427" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.157")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.355427" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.412621" level="INFO">${header_text} = ATM Branch</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.355427" elapsed="0.057194"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.412621" level="INFO">${cleaned_header_text} = ATMBranch</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:58:20.412621" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T12:58:20.416413" level="INFO">${cleaned_h_text} = ATMBranch</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.412621" elapsed="0.003792"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</if>
<var name="${i}">2</var>
<status status="PASS" start="2025-06-18T12:58:20.354336" elapsed="0.062077"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T12:58:20.416413" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.161")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.531269" level="INFO">${header_text} = Phone Number</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.114856"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T12:58:20.531269" level="INFO">${cleaned_header_text} = PhoneNumber</msg>
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T12:58:20.531269" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T12:58:20.531269" level="INFO">${cleaned_h_text} = PhoneNumber</msg>
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.531269" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.531269" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.536292" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.536925" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.536925" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.531269" elapsed="0.005656"/>
</branch>
<status status="PASS" start="2025-06-18T12:58:20.531269" elapsed="0.005656"/>
</if>
<var name="${i}">3</var>
<status status="PASS" start="2025-06-18T12:58:20.416413" elapsed="0.121542"/>
</iter>
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.538572" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-18T12:58:20.538572" level="INFO">${specific_header} = &lt;selenium.webdriver.remote.webelement.WebElement (session="433e04b69657647071a7a2f63e90a7ba", element="f.5AC09EEE61625C84F5E4B8B17D82A490.d.C8871138D6C4078B58E360DD61D3E1AE.e.164")&gt;</msg>
<var>${specific_header}</var>
<arg>${all_headers}</arg>
<arg>${i}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.538572" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.737802" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-911.png"&gt;&lt;img src="selenium-screenshot-911.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T12:58:20.737802" level="FAIL">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</msg>
<var>${header_text}</var>
<arg>${specific_header}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="FAIL" start="2025-06-18T12:58:20.540513" elapsed="0.212670">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</if>
<var name="${i}">4</var>
<status status="FAIL" start="2025-06-18T12:58:20.538572" elapsed="0.214611">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="FAIL" start="2025-06-18T12:58:20.304903" elapsed="0.448280">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Column with text '${COLUMN}' not found.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<var>${elements}</var>
<arg>xpath=*//tbody//tr//td[${index} + 1]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<var>${num_elements}</var>
<arg>${elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>False</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Get From List" owner="Collections">
<var>${specific_element}</var>
<arg>${elements}</arg>
<arg>${e}</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${element_text}</var>
<arg>${specific_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Search Result Retrieved: ${element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${element_t}</var>
<arg>${element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${search_key}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${SEARCH_KEY}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${search_k}</var>
<arg>${search_key}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$element_t == $search_k">
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------- Search Key was found in the correct column.</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</if>
<var name="${e}"/>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</iter>
<var>${e}</var>
<value>${num_elements}</value>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${found}' == 'False'</arg>
<arg>Fail</arg>
<arg>Search Key was NOT found in the correct column.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-18T12:58:20.753183" elapsed="0.000000"/>
</kw>
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="FAIL" start="2025-06-18T12:58:20.207582" elapsed="0.545601">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<arg>Validate Search - Zone SLA Column- on ATM Details</arg>
<arg>VMS_UAT</arg>
<arg>4</arg>
<arg>Zone SLA</arg>
<status status="FAIL" start="2025-06-18T12:57:20.046769" elapsed="60.706414">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T12:58:20.753183" elapsed="0.035247"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T12:58:20.788430" elapsed="0.000999"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T12:58:20.789429" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T12:58:20.789429" elapsed="8.898713"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T12:58:32.689164" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T12:58:29.688142" elapsed="3.001596"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-18T12:58:32.689738" elapsed="3.314768"/>
</kw>
<status status="PASS" start="2025-06-18T12:58:20.753183" elapsed="15.251323"/>
</kw>
<doc>Validate Search - Zone SLA Column- on ATM Details</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-18T12:57:20.045169" elapsed="75.960344">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</test>
<doc>Validate Search - Zone SLA Column- on ATM Details</doc>
<status status="FAIL" start="2025-06-18T12:57:14.592490" elapsed="81.417038"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">ATM DETAILS</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="0" fail="1" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-18T12:57:14.587186" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 177: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 207: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 238: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 277: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 321: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.512310" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 333: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.527935" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 376: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.534118" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 629: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.535144" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 651: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.536884" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 682: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 718: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 728: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 739: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 757: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 764: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 784: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 809: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.538238" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 840: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.550838" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 946: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.552857" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1172: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.554818" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1255: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.583721" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 116: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.583721" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 136: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.619988" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.622017" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 370: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T12:57:19.991153" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T12:57:20.022367" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-18T12:57:21.154273" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-18T12:57:21.154273" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T12:57:41.707285" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T12:57:51.734736" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T12:57:56.738719" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
